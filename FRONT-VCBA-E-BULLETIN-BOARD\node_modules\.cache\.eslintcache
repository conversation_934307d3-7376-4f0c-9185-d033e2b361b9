[{"D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\index.tsx": "1", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\reportWebVitals.ts": "2", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\App.tsx": "3", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\AdminAuthContext.tsx": "4", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\ToastContext.tsx": "5", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\StudentAuthContext.tsx": "6", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminDashboard.tsx": "7", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Settings.tsx": "8", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Calendar.tsx": "9", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\PostManagement.tsx": "10", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\debug\\ApiTest.tsx": "11", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\StudentManagement.tsx": "12", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\index.ts": "13", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminLayout.tsx": "14", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentLayout.tsx": "15", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\index.ts": "16", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\index.ts": "17", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\Toast.tsx": "18", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\student-auth.service.ts": "19", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\admin-auth.service.ts": "20", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\announcementService.ts": "21", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\studentService.ts": "22", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\calendarService.ts": "23", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\FacebookImageGallery.tsx": "24", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\AdminCommentSection.tsx": "25", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\NotificationBell.tsx": "26", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\config\\constants.ts": "27", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendar.ts": "28", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useNotificationNavigation.ts": "29", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\NotificationBell.tsx": "30", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\CommentSection.tsx": "31", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useAnnouncements.ts": "32", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ImageLightbox.tsx": "33", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementModal.tsx": "34", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementViewDialog.tsx": "35", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\CalendarEventModal.tsx": "36", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminSidebar.tsx": "37", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentHeader.tsx": "38", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminHeader.tsx": "39", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentSidebar.tsx": "40", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\ErrorBoundary.tsx": "41", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\PublicRoute.tsx": "42", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ProtectedRoute.tsx": "43", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\index.ts": "44", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\api.service.ts": "45", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useComments.ts": "46", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\notificationService.ts": "47", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\notificationNavigationService.ts": "48", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendarImageUpload.ts": "49", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useMultipleImageUpload.ts": "50", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\commentDepth.ts": "51", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\formUtils.ts": "52", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\MultipleImageUpload.tsx": "53", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\CascadingCategoryDropdown.tsx": "54", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\CalendarImageUpload.tsx": "55", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\index.ts": "56", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminRegister\\AdminRegister.tsx": "57", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminLogin\\AdminLogin.tsx": "58", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\index.ts": "59", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\commentService.ts": "60", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\auth.service.ts": "61", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\StudentLogin.tsx": "62", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\CalendarEventLikeButton.tsx": "63", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\calendarReactionService.ts": "64", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\HolidayManagement.tsx": "65", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\holidayService.ts": "66", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\ProfilePictureUpload.tsx": "67", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\WelcomePage.tsx": "68", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminNewsfeed.tsx": "69", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\NewsFeed.tsx": "70", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\StudentProfileSettingsModal.tsx": "71", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Archive.tsx": "72", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\archiveService.ts": "73", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\archive\\ArchivedCalendarEvents.tsx": "74", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\archive\\ArchivedStudents.tsx": "75", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\archive\\ArchivedAnnouncements.tsx": "76", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\tv\\TVDisplay.tsx": "77", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\tv\\TVSlideshow.tsx": "78", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\tv\\TVAnnouncement.tsx": "79", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\tv\\TVCalendarEvent.tsx": "80", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\tv-control\\TVControlPanel.tsx": "81", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\tvControlService.ts": "82", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\tv-control\\TVContentManager.tsx": "83", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\tv-control\\TVPlaybackControls.tsx": "84", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\tv-control\\TVStatusMonitor.tsx": "85", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\tv-control\\TVEmergencyBroadcast.tsx": "86", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\tv-control\\TVDisplaySettings.tsx": "87", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\permissions.ts": "88", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\AuthContext.tsx": "89", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminManagement.tsx": "90", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\CategoryManagement.tsx": "91", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\SMSSettings.tsx": "92", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AuditLogs.tsx": "93", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\BulkOperations.tsx": "94", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\AdminAccountList.tsx": "95", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\AdminAccountModal.tsx": "96", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\CategoryList.tsx": "97", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\CategoryModal.tsx": "98", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\categoryService.ts": "99", "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\api.ts": "100"}, {"size": 554, "mtime": *************, "results": "101", "hashOfConfig": "102"}, {"size": 419, "mtime": *************, "results": "103", "hashOfConfig": "102"}, {"size": 8134, "mtime": *************, "results": "104", "hashOfConfig": "102"}, {"size": 7830, "mtime": *************, "results": "105", "hashOfConfig": "102"}, {"size": 3769, "mtime": *************, "results": "106", "hashOfConfig": "102"}, {"size": 6136, "mtime": *************, "results": "107", "hashOfConfig": "102"}, {"size": 7447, "mtime": *************, "results": "108", "hashOfConfig": "102"}, {"size": 21118, "mtime": 1754414563128, "results": "109", "hashOfConfig": "102"}, {"size": 50360, "mtime": 1753139353321, "results": "110", "hashOfConfig": "102"}, {"size": 45894, "mtime": 1752867480062, "results": "111", "hashOfConfig": "102"}, {"size": 5757, "mtime": 1752391390000, "results": "112", "hashOfConfig": "102"}, {"size": 81742, "mtime": 1754410578323, "results": "113", "hashOfConfig": "102"}, {"size": 56, "mtime": 1753607885462, "results": "114", "hashOfConfig": "102"}, {"size": 1342, "mtime": 1751155290000, "results": "115", "hashOfConfig": "102"}, {"size": 1688, "mtime": 1751208620000, "results": "116", "hashOfConfig": "102"}, {"size": 103, "mtime": 1751140878000, "results": "117", "hashOfConfig": "102"}, {"size": 232, "mtime": 1751541102000, "results": "118", "hashOfConfig": "102"}, {"size": 3996, "mtime": 1751807708000, "results": "119", "hashOfConfig": "102"}, {"size": 8153, "mtime": 1753729692309, "results": "120", "hashOfConfig": "102"}, {"size": 16125, "mtime": 1753159156475, "results": "121", "hashOfConfig": "102"}, {"size": 17132, "mtime": *************, "results": "122", "hashOfConfig": "102"}, {"size": 7759, "mtime": 1753726439666, "results": "123", "hashOfConfig": "102"}, {"size": 14456, "mtime": 1753728961926, "results": "124", "hashOfConfig": "102"}, {"size": 8587, "mtime": 1752337674000, "results": "125", "hashOfConfig": "102"}, {"size": 25200, "mtime": 1753729065843, "results": "126", "hashOfConfig": "102"}, {"size": 12906, "mtime": 1752333634000, "results": "127", "hashOfConfig": "102"}, {"size": 6264, "mtime": 1753735348071, "results": "128", "hashOfConfig": "102"}, {"size": 9834, "mtime": 1753077700688, "results": "129", "hashOfConfig": "102"}, {"size": 8214, "mtime": 1752334762000, "results": "130", "hashOfConfig": "102"}, {"size": 12982, "mtime": 1752333550000, "results": "131", "hashOfConfig": "102"}, {"size": 19475, "mtime": 1753729131110, "results": "132", "hashOfConfig": "102"}, {"size": 15593, "mtime": 1752760674000, "results": "133", "hashOfConfig": "102"}, {"size": 12325, "mtime": 1752330204000, "results": "134", "hashOfConfig": "102"}, {"size": 28178, "mtime": 1753079518460, "results": "135", "hashOfConfig": "102"}, {"size": 24168, "mtime": 1752867418410, "results": "136", "hashOfConfig": "102"}, {"size": 39901, "mtime": 1753141345895, "results": "137", "hashOfConfig": "102"}, {"size": 11323, "mtime": 1754412636651, "results": "138", "hashOfConfig": "102"}, {"size": 9059, "mtime": 1753730582696, "results": "139", "hashOfConfig": "102"}, {"size": 17223, "mtime": 1754414290097, "results": "140", "hashOfConfig": "102"}, {"size": 5633, "mtime": 1753730655191, "results": "141", "hashOfConfig": "102"}, {"size": 2063, "mtime": 1751140856000, "results": "142", "hashOfConfig": "102"}, {"size": 2236, "mtime": 1753600593548, "results": "143", "hashOfConfig": "102"}, {"size": 4237, "mtime": 1753600593548, "results": "144", "hashOfConfig": "102"}, {"size": 230, "mtime": 1751371668000, "results": "145", "hashOfConfig": "102"}, {"size": 10813, "mtime": 1752761372000, "results": "146", "hashOfConfig": "102"}, {"size": 14506, "mtime": 1752879590759, "results": "147", "hashOfConfig": "102"}, {"size": 26448, "mtime": 1752380598000, "results": "148", "hashOfConfig": "102"}, {"size": 10147, "mtime": 1752334796000, "results": "149", "hashOfConfig": "102"}, {"size": 10510, "mtime": 1752310980000, "results": "150", "hashOfConfig": "102"}, {"size": 10877, "mtime": 1752092600000, "results": "151", "hashOfConfig": "102"}, {"size": 7318, "mtime": 1752381124000, "results": "152", "hashOfConfig": "102"}, {"size": 5263, "mtime": 1752867135753, "results": "153", "hashOfConfig": "102"}, {"size": 19520, "mtime": 1752338090000, "results": "154", "hashOfConfig": "102"}, {"size": 13444, "mtime": 1752870507174, "results": "155", "hashOfConfig": "102"}, {"size": 16870, "mtime": 1752338106000, "results": "156", "hashOfConfig": "102"}, {"size": 616, "mtime": 1752865556056, "results": "157", "hashOfConfig": "102"}, {"size": 15760, "mtime": 1752828048867, "results": "158", "hashOfConfig": "102"}, {"size": 9958, "mtime": 1751375132000, "results": "159", "hashOfConfig": "102"}, {"size": 42, "mtime": 1751129052000, "results": "160", "hashOfConfig": "102"}, {"size": 20869, "mtime": 1752885135797, "results": "161", "hashOfConfig": "102"}, {"size": 10085, "mtime": 1753141914993, "results": "162", "hashOfConfig": "102"}, {"size": 9297, "mtime": 1751476824000, "results": "163", "hashOfConfig": "102"}, {"size": 4237, "mtime": 1753722668541, "results": "164", "hashOfConfig": "102"}, {"size": 3686, "mtime": 1752890875441, "results": "165", "hashOfConfig": "102"}, {"size": 18927, "mtime": 1753141219821, "results": "166", "hashOfConfig": "102"}, {"size": 7423, "mtime": 1753077217399, "results": "167", "hashOfConfig": "102"}, {"size": 18379, "mtime": 1753155927801, "results": "168", "hashOfConfig": "102"}, {"size": 18988, "mtime": 1753609851189, "results": "169", "hashOfConfig": "102"}, {"size": 90955, "mtime": 1753616469959, "results": "170", "hashOfConfig": "102"}, {"size": 104542, "mtime": 1753730873531, "results": "171", "hashOfConfig": "102"}, {"size": 30091, "mtime": 1753732118317, "results": "172", "hashOfConfig": "102"}, {"size": 9960, "mtime": 1753735972583, "results": "173", "hashOfConfig": "102"}, {"size": 6463, "mtime": 1753736254532, "results": "174", "hashOfConfig": "102"}, {"size": 16384, "mtime": 1753735939665, "results": "175", "hashOfConfig": "102"}, {"size": 17240, "mtime": 1753735956350, "results": "176", "hashOfConfig": "102"}, {"size": 16677, "mtime": 1753735924469, "results": "177", "hashOfConfig": "102"}, {"size": 14569, "mtime": 1754018974966, "results": "178", "hashOfConfig": "102"}, {"size": 7457, "mtime": 1754019701685, "results": "179", "hashOfConfig": "102"}, {"size": 11910, "mtime": 1754020518834, "results": "180", "hashOfConfig": "102"}, {"size": 16995, "mtime": 1754020537961, "results": "181", "hashOfConfig": "102"}, {"size": 6637, "mtime": 1753998161360, "results": "182", "hashOfConfig": "102"}, {"size": 8154, "mtime": 1754018255515, "results": "183", "hashOfConfig": "102"}, {"size": 11027, "mtime": 1753997495633, "results": "184", "hashOfConfig": "102"}, {"size": 8928, "mtime": 1753997414781, "results": "185", "hashOfConfig": "102"}, {"size": 12092, "mtime": 1753997586877, "results": "186", "hashOfConfig": "102"}, {"size": 9584, "mtime": 1754018489138, "results": "187", "hashOfConfig": "102"}, {"size": 9550, "mtime": 1753997452188, "results": "188", "hashOfConfig": "102"}, {"size": 8618, "mtime": 1754410542102, "results": "189", "hashOfConfig": "102"}, {"size": 8587, "mtime": 1751374598000, "results": "190", "hashOfConfig": "102"}, {"size": 15961, "mtime": 1754414349507, "results": "191", "hashOfConfig": "102"}, {"size": 22084, "mtime": 1754415667298, "results": "192", "hashOfConfig": "102"}, {"size": 18260, "mtime": 1754396565757, "results": "193", "hashOfConfig": "102"}, {"size": 22101, "mtime": 1754411925693, "results": "194", "hashOfConfig": "102"}, {"size": 21184, "mtime": 1754411548324, "results": "195", "hashOfConfig": "102"}, {"size": 14612, "mtime": 1754414659592, "results": "196", "hashOfConfig": "102"}, {"size": 21889, "mtime": 1754415000937, "results": "197", "hashOfConfig": "102"}, {"size": 20601, "mtime": 1754415486074, "results": "198", "hashOfConfig": "102"}, {"size": 16611, "mtime": 1754415500851, "results": "199", "hashOfConfig": "102"}, {"size": 8963, "mtime": 1754415456562, "results": "200", "hashOfConfig": "102"}, {"size": 960, "mtime": 1754414011607, "results": "201", "hashOfConfig": "102"}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ycd4oj", {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\index.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\reportWebVitals.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\App.tsx", ["502", "503"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\AdminAuthContext.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\ToastContext.tsx", ["504"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\StudentAuthContext.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminDashboard.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Settings.tsx", ["505", "506", "507", "508"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Calendar.tsx", ["509", "510", "511", "512"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\PostManagement.tsx", ["513", "514", "515"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\debug\\ApiTest.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\StudentManagement.tsx", ["516", "517", "518"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminLayout.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentLayout.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\Toast.tsx", ["519"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\student-auth.service.ts", ["520"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\admin-auth.service.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\announcementService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\studentService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\calendarService.ts", ["521"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\FacebookImageGallery.tsx", ["522"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\AdminCommentSection.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\NotificationBell.tsx", ["523"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\config\\constants.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendar.ts", ["524", "525", "526"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useNotificationNavigation.ts", ["527"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\NotificationBell.tsx", ["528"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\CommentSection.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useAnnouncements.ts", ["529", "530", "531", "532", "533"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ImageLightbox.tsx", ["534", "535", "536", "537"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementModal.tsx", ["538"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementViewDialog.tsx", ["539", "540", "541", "542"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\CalendarEventModal.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminSidebar.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentHeader.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminHeader.tsx", ["543", "544"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentSidebar.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\ErrorBoundary.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\PublicRoute.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ProtectedRoute.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\api.service.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useComments.ts", ["545", "546", "547", "548", "549"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\notificationService.ts", ["550", "551", "552", "553", "554"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\notificationNavigationService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendarImageUpload.ts", ["555", "556", "557", "558", "559", "560"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useMultipleImageUpload.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\commentDepth.ts", ["561", "562", "563"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\formUtils.ts", ["564"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\MultipleImageUpload.tsx", ["565", "566"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\CascadingCategoryDropdown.tsx", ["567"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\CalendarImageUpload.tsx", ["568", "569", "570"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminRegister\\AdminRegister.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminLogin\\AdminLogin.tsx", ["571", "572", "573"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\index.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\commentService.ts", ["574"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\auth.service.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\StudentLogin.tsx", ["575", "576", "577"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\CalendarEventLikeButton.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\calendarReactionService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\HolidayManagement.tsx", ["578", "579", "580"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\holidayService.ts", ["581"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\ProfilePictureUpload.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\WelcomePage.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminNewsfeed.tsx", ["582", "583", "584", "585", "586", "587"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\NewsFeed.tsx", ["588"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\StudentProfileSettingsModal.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Archive.tsx", ["589", "590", "591", "592"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\archiveService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\archive\\ArchivedCalendarEvents.tsx", ["593"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\archive\\ArchivedStudents.tsx", ["594"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\archive\\ArchivedAnnouncements.tsx", ["595"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\tv\\TVDisplay.tsx", ["596", "597"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\tv\\TVSlideshow.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\tv\\TVAnnouncement.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\tv\\TVCalendarEvent.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\tv-control\\TVControlPanel.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\tvControlService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\tv-control\\TVContentManager.tsx", ["598", "599", "600"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\tv-control\\TVPlaybackControls.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\tv-control\\TVStatusMonitor.tsx", ["601"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\tv-control\\TVEmergencyBroadcast.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\tv-control\\TVDisplaySettings.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\permissions.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\AuthContext.tsx", ["602"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminManagement.tsx", ["603", "604", "605"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\CategoryManagement.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\SMSSettings.tsx", ["606", "607"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AuditLogs.tsx", ["608", "609"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\BulkOperations.tsx", ["610"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\AdminAccountList.tsx", ["611", "612", "613", "614"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\AdminAccountModal.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\CategoryList.tsx", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\CategoryModal.tsx", ["615"], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\categoryService.ts", [], [], "D:\\online-e-bulletin-reactjs\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\api.ts", [], [], {"ruleId": "616", "severity": 1, "message": "617", "line": 3, "column": 10, "nodeType": "618", "messageId": "619", "endLine": 3, "endColumn": 22}, {"ruleId": "616", "severity": 1, "message": "620", "line": 26, "column": 8, "nodeType": "618", "messageId": "619", "endLine": 26, "endColumn": 21}, {"ruleId": "616", "severity": 1, "message": "621", "line": 2, "column": 28, "nodeType": "618", "messageId": "619", "endLine": 2, "endColumn": 38}, {"ruleId": "616", "severity": 1, "message": "622", "line": 4, "column": 10, "nodeType": "618", "messageId": "619", "endLine": 4, "endColumn": 14}, {"ruleId": "616", "severity": 1, "message": "623", "line": 4, "column": 28, "nodeType": "618", "messageId": "619", "endLine": 4, "endColumn": 40}, {"ruleId": "616", "severity": 1, "message": "624", "line": 4, "column": 48, "nodeType": "618", "messageId": "619", "endLine": 4, "endColumn": 52}, {"ruleId": "616", "severity": 1, "message": "625", "line": 10, "column": 9, "nodeType": "618", "messageId": "619", "endLine": 10, "endColumn": 20}, {"ruleId": "616", "severity": 1, "message": "626", "line": 6, "column": 132, "nodeType": "618", "messageId": "619", "endLine": 6, "endColumn": 137}, {"ruleId": "627", "severity": 1, "message": "628", "line": 217, "column": 6, "nodeType": "629", "endLine": 217, "endColumn": 57, "suggestions": "630"}, {"ruleId": "627", "severity": 1, "message": "631", "line": 217, "column": 7, "nodeType": "632", "endLine": 217, "endColumn": 32}, {"ruleId": "627", "severity": 1, "message": "631", "line": 217, "column": 34, "nodeType": "632", "endLine": 217, "endColumn": 56}, {"ruleId": "616", "severity": 1, "message": "633", "line": 53, "column": 19, "nodeType": "618", "messageId": "619", "endLine": 53, "endColumn": 29}, {"ruleId": "616", "severity": 1, "message": "634", "line": 83, "column": 5, "nodeType": "618", "messageId": "619", "endLine": 83, "endColumn": 10}, {"ruleId": "627", "severity": 1, "message": "635", "line": 162, "column": 6, "nodeType": "629", "endLine": 162, "endColumn": 82, "suggestions": "636"}, {"ruleId": "616", "severity": 1, "message": "637", "line": 2, "column": 57, "nodeType": "618", "messageId": "619", "endLine": 2, "endColumn": 73}, {"ruleId": "616", "severity": 1, "message": "638", "line": 20, "column": 10, "nodeType": "618", "messageId": "619", "endLine": 20, "endColumn": 23}, {"ruleId": "627", "severity": 1, "message": "639", "line": 121, "column": 6, "nodeType": "629", "endLine": 121, "endColumn": 60, "suggestions": "640"}, {"ruleId": "627", "severity": 1, "message": "641", "line": 39, "column": 6, "nodeType": "629", "endLine": 39, "endColumn": 16, "suggestions": "642"}, {"ruleId": "616", "severity": 1, "message": "643", "line": 35, "column": 32, "nodeType": "618", "messageId": "619", "endLine": 35, "endColumn": 33}, {"ruleId": "616", "severity": 1, "message": "644", "line": 3, "column": 25, "nodeType": "618", "messageId": "619", "endLine": 3, "endColumn": 37}, {"ruleId": "627", "severity": 1, "message": "645", "line": 64, "column": 6, "nodeType": "629", "endLine": 64, "endColumn": 17, "suggestions": "646"}, {"ruleId": "616", "severity": 1, "message": "647", "line": 93, "column": 9, "nodeType": "618", "messageId": "619", "endLine": 93, "endColumn": 19}, {"ruleId": "616", "severity": 1, "message": "648", "line": 7, "column": 3, "nodeType": "618", "messageId": "619", "endLine": 7, "endColumn": 15}, {"ruleId": "627", "severity": 1, "message": "649", "line": 82, "column": 6, "nodeType": "629", "endLine": 82, "endColumn": 33, "suggestions": "650"}, {"ruleId": "627", "severity": 1, "message": "651", "line": 82, "column": 7, "nodeType": "632", "endLine": 82, "endColumn": 32}, {"ruleId": "627", "severity": 1, "message": "652", "line": 57, "column": 6, "nodeType": "629", "endLine": 57, "endColumn": 16, "suggestions": "653"}, {"ruleId": "616", "severity": 1, "message": "647", "line": 93, "column": 9, "nodeType": "618", "messageId": "619", "endLine": 93, "endColumn": 19}, {"ruleId": "616", "severity": 1, "message": "654", "line": 2, "column": 31, "nodeType": "618", "messageId": "619", "endLine": 2, "endColumn": 55}, {"ruleId": "616", "severity": 1, "message": "655", "line": 8, "column": 15, "nodeType": "618", "messageId": "619", "endLine": 8, "endColumn": 26}, {"ruleId": "627", "severity": 1, "message": "656", "line": 121, "column": 6, "nodeType": "629", "endLine": 121, "endColumn": 105, "suggestions": "657"}, {"ruleId": "627", "severity": 1, "message": "651", "line": 121, "column": 7, "nodeType": "632", "endLine": 121, "endColumn": 30}, {"ruleId": "627", "severity": 1, "message": "658", "line": 324, "column": 6, "nodeType": "629", "endLine": 324, "endColumn": 48, "suggestions": "659"}, {"ruleId": "616", "severity": 1, "message": "660", "line": 3, "column": 10, "nodeType": "618", "messageId": "619", "endLine": 3, "endColumn": 21}, {"ruleId": "627", "severity": 1, "message": "645", "line": 56, "column": 6, "nodeType": "629", "endLine": 56, "endColumn": 17, "suggestions": "661"}, {"ruleId": "616", "severity": 1, "message": "662", "line": 147, "column": 10, "nodeType": "618", "messageId": "619", "endLine": 147, "endColumn": 21}, {"ruleId": "627", "severity": 1, "message": "663", "line": 180, "column": 6, "nodeType": "629", "endLine": 180, "endColumn": 23, "suggestions": "664"}, {"ruleId": "627", "severity": 1, "message": "665", "line": 182, "column": 6, "nodeType": "629", "endLine": 182, "endColumn": 113, "suggestions": "666"}, {"ruleId": "616", "severity": 1, "message": "667", "line": 2, "column": 34, "nodeType": "618", "messageId": "619", "endLine": 2, "endColumn": 37}, {"ruleId": "627", "severity": 1, "message": "645", "line": 65, "column": 6, "nodeType": "629", "endLine": 65, "endColumn": 17, "suggestions": "668"}, {"ruleId": "669", "severity": 1, "message": "670", "line": 248, "column": 11, "nodeType": "671", "endLine": 262, "endColumn": 13}, {"ruleId": "669", "severity": 1, "message": "670", "line": 332, "column": 19, "nodeType": "671", "endLine": 346, "endColumn": 21}, {"ruleId": "616", "severity": 1, "message": "622", "line": 6, "column": 73, "nodeType": "618", "messageId": "619", "endLine": 6, "endColumn": 77}, {"ruleId": "616", "severity": 1, "message": "672", "line": 16, "column": 9, "nodeType": "618", "messageId": "619", "endLine": 16, "endColumn": 17}, {"ruleId": "616", "severity": 1, "message": "673", "line": 2, "column": 10, "nodeType": "618", "messageId": "619", "endLine": 2, "endColumn": 24}, {"ruleId": "616", "severity": 1, "message": "674", "line": 12, "column": 3, "nodeType": "618", "messageId": "619", "endLine": 12, "endColumn": 17}, {"ruleId": "616", "severity": 1, "message": "675", "line": 13, "column": 3, "nodeType": "618", "messageId": "619", "endLine": 13, "endColumn": 28}, {"ruleId": "627", "severity": 1, "message": "676", "line": 140, "column": 6, "nodeType": "629", "endLine": 140, "endColumn": 57, "suggestions": "677"}, {"ruleId": "678", "severity": 1, "message": "679", "line": 426, "column": 44, "nodeType": "680", "messageId": "681", "endLine": 426, "endColumn": 98}, {"ruleId": "616", "severity": 1, "message": "682", "line": 1, "column": 22, "nodeType": "618", "messageId": "619", "endLine": 1, "endColumn": 37}, {"ruleId": "616", "severity": 1, "message": "683", "line": 1, "column": 39, "nodeType": "618", "messageId": "619", "endLine": 1, "endColumn": 56}, {"ruleId": "616", "severity": 1, "message": "684", "line": 2, "column": 10, "nodeType": "618", "messageId": "619", "endLine": 2, "endColumn": 26}, {"ruleId": "685", "severity": 1, "message": "686", "line": 581, "column": 3, "nodeType": "687", "messageId": "688", "endLine": 583, "endColumn": 4}, {"ruleId": "685", "severity": 1, "message": "686", "line": 647, "column": 3, "nodeType": "687", "messageId": "688", "endLine": 649, "endColumn": 4}, {"ruleId": "627", "severity": 1, "message": "689", "line": 128, "column": 6, "nodeType": "629", "endLine": 128, "endColumn": 18, "suggestions": "690"}, {"ruleId": "627", "severity": 1, "message": "691", "line": 173, "column": 6, "nodeType": "629", "endLine": 173, "endColumn": 33, "suggestions": "692"}, {"ruleId": "627", "severity": 1, "message": "691", "line": 204, "column": 6, "nodeType": "629", "endLine": 204, "endColumn": 21, "suggestions": "693"}, {"ruleId": "627", "severity": 1, "message": "691", "line": 239, "column": 6, "nodeType": "629", "endLine": 239, "endColumn": 33, "suggestions": "694"}, {"ruleId": "627", "severity": 1, "message": "695", "line": 299, "column": 6, "nodeType": "629", "endLine": 299, "endColumn": 22, "suggestions": "696"}, {"ruleId": "627", "severity": 1, "message": "697", "line": 306, "column": 6, "nodeType": "629", "endLine": 306, "endColumn": 18, "suggestions": "698"}, {"ruleId": "678", "severity": 1, "message": "679", "line": 35, "column": 44, "nodeType": "680", "messageId": "681", "endLine": 35, "endColumn": 98}, {"ruleId": "678", "severity": 1, "message": "699", "line": 96, "column": 46, "nodeType": "680", "messageId": "681", "endLine": 96, "endColumn": 97}, {"ruleId": "700", "severity": 1, "message": "701", "line": 234, "column": 1, "nodeType": "702", "endLine": 248, "endColumn": 3}, {"ruleId": "616", "severity": 1, "message": "703", "line": 23, "column": 11, "nodeType": "618", "messageId": "619", "endLine": 23, "endColumn": 28}, {"ruleId": "627", "severity": 1, "message": "645", "line": 100, "column": 6, "nodeType": "629", "endLine": 100, "endColumn": 17, "suggestions": "704"}, {"ruleId": "627", "severity": 1, "message": "705", "line": 375, "column": 6, "nodeType": "629", "endLine": 375, "endColumn": 8, "suggestions": "706"}, {"ruleId": "707", "severity": 1, "message": "708", "line": 259, "column": 7, "nodeType": "671", "endLine": 273, "endColumn": 8}, {"ruleId": "627", "severity": 1, "message": "645", "line": 66, "column": 6, "nodeType": "629", "endLine": 66, "endColumn": 17, "suggestions": "709"}, {"ruleId": "616", "severity": 1, "message": "710", "line": 385, "column": 9, "nodeType": "618", "messageId": "619", "endLine": 385, "endColumn": 24}, {"ruleId": "627", "severity": 1, "message": "711", "line": 404, "column": 6, "nodeType": "629", "endLine": 404, "endColumn": 8, "suggestions": "712"}, {"ruleId": "616", "severity": 1, "message": "713", "line": 2, "column": 10, "nodeType": "618", "messageId": "619", "endLine": 2, "endColumn": 14}, {"ruleId": "616", "severity": 1, "message": "714", "line": 90, "column": 9, "nodeType": "618", "messageId": "619", "endLine": 90, "endColumn": 33}, {"ruleId": "616", "severity": 1, "message": "715", "line": 94, "column": 9, "nodeType": "618", "messageId": "619", "endLine": 94, "endColumn": 26}, {"ruleId": "616", "severity": 1, "message": "716", "line": 4, "column": 27, "nodeType": "618", "messageId": "619", "endLine": 4, "endColumn": 39}, {"ruleId": "616", "severity": 1, "message": "713", "line": 2, "column": 10, "nodeType": "618", "messageId": "619", "endLine": 2, "endColumn": 14}, {"ruleId": "616", "severity": 1, "message": "714", "line": 105, "column": 9, "nodeType": "618", "messageId": "619", "endLine": 105, "endColumn": 33}, {"ruleId": "616", "severity": 1, "message": "717", "line": 110, "column": 9, "nodeType": "618", "messageId": "619", "endLine": 110, "endColumn": 16}, {"ruleId": "616", "severity": 1, "message": "718", "line": 2, "column": 49, "nodeType": "618", "messageId": "619", "endLine": 2, "endColumn": 60}, {"ruleId": "616", "severity": 1, "message": "667", "line": 3, "column": 57, "nodeType": "618", "messageId": "619", "endLine": 3, "endColumn": 60}, {"ruleId": "627", "severity": 1, "message": "719", "line": 40, "column": 6, "nodeType": "629", "endLine": 40, "endColumn": 19, "suggestions": "720"}, {"ruleId": "616", "severity": 1, "message": "721", "line": 2, "column": 10, "nodeType": "618", "messageId": "619", "endLine": 2, "endColumn": 21}, {"ruleId": "616", "severity": 1, "message": "722", "line": 3, "column": 10, "nodeType": "618", "messageId": "619", "endLine": 3, "endColumn": 29}, {"ruleId": "616", "severity": 1, "message": "723", "line": 23, "column": 3, "nodeType": "618", "messageId": "619", "endLine": 23, "endColumn": 7}, {"ruleId": "627", "severity": 1, "message": "645", "line": 101, "column": 6, "nodeType": "629", "endLine": 101, "endColumn": 17, "suggestions": "724"}, {"ruleId": "616", "severity": 1, "message": "725", "line": 473, "column": 31, "nodeType": "618", "messageId": "619", "endLine": 473, "endColumn": 45}, {"ruleId": "616", "severity": 1, "message": "726", "line": 536, "column": 9, "nodeType": "618", "messageId": "619", "endLine": 536, "endColumn": 28}, {"ruleId": "616", "severity": 1, "message": "727", "line": 702, "column": 9, "nodeType": "618", "messageId": "619", "endLine": 702, "endColumn": 24}, {"ruleId": "616", "severity": 1, "message": "728", "line": 4, "column": 10, "nodeType": "618", "messageId": "619", "endLine": 4, "endColumn": 33}, {"ruleId": "616", "severity": 1, "message": "729", "line": 2, "column": 61, "nodeType": "618", "messageId": "619", "endLine": 2, "endColumn": 70}, {"ruleId": "616", "severity": 1, "message": "730", "line": 2, "column": 72, "nodeType": "618", "messageId": "619", "endLine": 2, "endColumn": 78}, {"ruleId": "616", "severity": 1, "message": "731", "line": 2, "column": 80, "nodeType": "618", "messageId": "619", "endLine": 2, "endColumn": 86}, {"ruleId": "616", "severity": 1, "message": "732", "line": 2, "column": 88, "nodeType": "618", "messageId": "619", "endLine": 2, "endColumn": 94}, {"ruleId": "627", "severity": 1, "message": "733", "line": 23, "column": 6, "nodeType": "629", "endLine": 23, "endColumn": 31, "suggestions": "734"}, {"ruleId": "627", "severity": 1, "message": "735", "line": 23, "column": 6, "nodeType": "629", "endLine": 23, "endColumn": 31, "suggestions": "736"}, {"ruleId": "627", "severity": 1, "message": "737", "line": 24, "column": 6, "nodeType": "629", "endLine": 24, "endColumn": 31, "suggestions": "738"}, {"ruleId": "627", "severity": 1, "message": "739", "line": 148, "column": 6, "nodeType": "629", "endLine": 148, "endColumn": 31, "suggestions": "740"}, {"ruleId": "627", "severity": 1, "message": "741", "line": 164, "column": 6, "nodeType": "629", "endLine": 164, "endColumn": 8, "suggestions": "742"}, {"ruleId": "616", "severity": 1, "message": "743", "line": 1, "column": 27, "nodeType": "618", "messageId": "619", "endLine": 1, "endColumn": 36}, {"ruleId": "616", "severity": 1, "message": "744", "line": 18, "column": 14, "nodeType": "618", "messageId": "619", "endLine": 18, "endColumn": 34}, {"ruleId": "616", "severity": 1, "message": "745", "line": 30, "column": 14, "nodeType": "618", "messageId": "619", "endLine": 30, "endColumn": 27}, {"ruleId": "616", "severity": 1, "message": "746", "line": 3, "column": 52, "nodeType": "618", "messageId": "619", "endLine": 3, "endColumn": 61}, {"ruleId": "616", "severity": 1, "message": "747", "line": 167, "column": 13, "nodeType": "618", "messageId": "619", "endLine": 167, "endColumn": 25}, {"ruleId": "616", "severity": 1, "message": "732", "line": 2, "column": 28, "nodeType": "618", "messageId": "619", "endLine": 2, "endColumn": 34}, {"ruleId": "616", "severity": 1, "message": "748", "line": 5, "column": 10, "nodeType": "618", "messageId": "619", "endLine": 5, "endColumn": 32}, {"ruleId": "627", "severity": 1, "message": "749", "line": 64, "column": 6, "nodeType": "629", "endLine": 64, "endColumn": 104, "suggestions": "750"}, {"ruleId": "616", "severity": 1, "message": "751", "line": 42, "column": 10, "nodeType": "618", "messageId": "619", "endLine": 42, "endColumn": 19}, {"ruleId": "616", "severity": 1, "message": "752", "line": 42, "column": 21, "nodeType": "618", "messageId": "619", "endLine": 42, "endColumn": 33}, {"ruleId": "616", "severity": 1, "message": "731", "line": 2, "column": 18, "nodeType": "618", "messageId": "619", "endLine": 2, "endColumn": 24}, {"ruleId": "616", "severity": 1, "message": "753", "line": 32, "column": 24, "nodeType": "618", "messageId": "619", "endLine": 32, "endColumn": 39}, {"ruleId": "616", "severity": 1, "message": "754", "line": 2, "column": 45, "nodeType": "618", "messageId": "619", "endLine": 2, "endColumn": 53}, {"ruleId": "616", "severity": 1, "message": "755", "line": 2, "column": 43, "nodeType": "618", "messageId": "619", "endLine": 2, "endColumn": 47}, {"ruleId": "616", "severity": 1, "message": "756", "line": 2, "column": 49, "nodeType": "618", "messageId": "619", "endLine": 2, "endColumn": 54}, {"ruleId": "616", "severity": 1, "message": "754", "line": 2, "column": 56, "nodeType": "618", "messageId": "619", "endLine": 2, "endColumn": 64}, {"ruleId": "616", "severity": 1, "message": "757", "line": 2, "column": 66, "nodeType": "618", "messageId": "619", "endLine": 2, "endColumn": 72}, {"ruleId": "627", "severity": 1, "message": "758", "line": 86, "column": 6, "nodeType": "629", "endLine": 86, "endColumn": 43, "suggestions": "759"}, "@typescript-eslint/no-unused-vars", "'AuthProvider' is defined but never used.", "Identifier", "unusedVar", "'StudentLayout' is defined but never used.", "'ToastProps' is defined but never used.", "'User' is defined but never used.", "'SettingsIcon' is defined but never used.", "'Bell' is defined but never used.", "'permissions' is assigned a value but never used.", "'Heart' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useMemo has a missing dependency: 'currentDate'. Either include it or remove the dependency array.", "ArrayExpression", ["760"], "React Hook useMemo has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "CallExpression", "'setFilters' is assigned a value but never used.", "'error' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'filters' and 'updateFilters'. Either include them or remove the dependency array.", ["761"], "'StudentsResponse' is defined but never used.", "'totalStudents' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'permissions.isSuperAdmin'. Either include it or remove the dependency array.", ["762"], "React Hook useEffect has a missing dependency: 'handleClose'. Either include it or remove the dependency array.", ["763"], "'T' is defined but never used.", "'API_BASE_URL' is defined but never used.", "React Hook useEffect has a missing dependency: 'imageUrl'. Either include it or remove the dependency array.", ["764"], "'markAsRead' is assigned a value but never used.", "'EventFilters' is defined but never used.", "React Hook useCallback has a missing dependency: 'currentDate'. Either include it or remove the dependency array.", ["765"], "React Hook useCallback has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "React Hook useEffect has a missing dependency: 'handleDeepLinkHighlighting'. Either include it or remove the dependency array.", ["766"], "'adminAnnouncementService' is defined but never used.", "'Subcategory' is defined but never used.", "React Hook useCallback has a missing dependency: 'filters'. Either include it or remove the dependency array.", ["767"], "React Hook useEffect has a missing dependency: 'fetchAnnouncements'. Either include it or remove the dependency array.", ["768"], "'getImageUrl' is defined but never used.", ["769"], "'imageLoaded' is assigned a value but never used.", "React Hook useCallback has missing dependencies: 'goToNext' and 'goToPrevious'. Either include them or remove the dependency array.", ["770"], "React Hook useEffect has missing dependencies: 'announcement' and 'refreshImages'. Either include them or remove the dependency array.", ["771"], "'Eye' is defined but never used.", ["772"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'navigate' is assigned a value but never used.", "'commentService' is defined but never used.", "'CommentFilters' is defined but never used.", "'PaginatedCommentsResponse' is defined but never used.", "React Hook useCallback has an unnecessary dependency: 'calendarId'. Either exclude it or remove the dependency array.", ["773"], "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'currentComment'.", "ArrowFunctionExpression", "unsafeRefs", "'adminHttpClient' is defined but never used.", "'studentHttpClient' is defined but never used.", "'AdminAuthService' is defined but never used.", "@typescript-eslint/no-useless-constructor", "Useless constructor.", "MethodDefinition", "noUselessConstructor", "React Hook useCallback has a missing dependency: 'onError'. Either include it or remove the dependency array. If 'onError' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["774"], "React Hook useCallback has missing dependencies: 'onError' and 'onSuccess'. Either include them or remove the dependency array. If 'onSuccess' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["775"], ["776"], ["777"], "React Hook useCallback has a missing dependency: 'refreshImages'. Either include it or remove the dependency array.", ["778"], "React Hook useEffect has a missing dependency: 'refreshImages'. Either include it or remove the dependency array.", ["779"], "Function declared in a loop contains unsafe references to variable(s) 'rootComment'.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'skipScheduledDate' is assigned a value but never used.", ["780"], "React Hook useEffect has a missing dependency: 'images'. Either include it or remove the dependency array.", ["781"], "jsx-a11y/role-supports-aria-props", "The attribute aria-required is not supported by the role button.", ["782"], "'setPrimaryImage' is assigned a value but never used.", "React Hook React.useEffect has a missing dependency: 'images'. Either include it or remove the dependency array.", ["783"], "'Link' is defined but never used.", "'togglePasswordVisibility' is assigned a value but never used.", "'handleForceLogout' is assigned a value but never used.", "'ReactionType' is defined but never used.", "'isEmail' is assigned a value but never used.", "'SyncResults' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array.", ["784"], "'ApiResponse' is defined but never used.", "'announcementService' is defined but never used.", "'Edit' is defined but never used.", ["785"], "'notificationId' is assigned a value but never used.", "'fetchRecentStudents' is assigned a value but never used.", "'combinedContent' is assigned a value but never used.", "'calendarReactionService' is defined but never used.", "'RotateCcw' is defined but never used.", "'Trash2' is defined but never used.", "'Search' is defined but never used.", "'Filter' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadEvents'. Either include it or remove the dependency array.", ["786"], "React Hook useEffect has a missing dependency: 'loadStudents'. Either include it or remove the dependency array.", ["787"], "React Hook useEffect has a missing dependency: 'loadAnnouncements'. Either include it or remove the dependency array.", ["788"], "React Hook useEffect has a missing dependency: 'createSlideContent'. Either include it or remove the dependency array.", ["789"], "React Hook useEffect has a missing dependency: 'handleControlCommand'. Either include it or remove the dependency array.", ["790"], "'useEffect' is defined but never used.", "'announcementsLoading' is assigned a value but never used.", "'eventsLoading' is assigned a value but never used.", "'RefreshCw' is defined but never used.", "'redirectPath' is assigned a value but never used.", "'adminManagementService' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadAdmins'. Either include it or remove the dependency array.", ["791"], "'templates' is assigned a value but never used.", "'setTemplates' is assigned a value but never used.", "'setSelectedUser' is assigned a value but never used.", "'Calendar' is defined but never used.", "'Mail' is defined but never used.", "'Phone' is defined but never used.", "'Shield' is defined but never used.", "React Hook useEffect has missing dependencies: 'isEditMode' and 'isSubcategoryMode'. Either include them or remove the dependency array.", ["792"], {"desc": "793", "fix": "794"}, {"desc": "795", "fix": "796"}, {"desc": "797", "fix": "798"}, {"desc": "799", "fix": "800"}, {"desc": "801", "fix": "802"}, {"desc": "793", "fix": "803"}, {"desc": "804", "fix": "805"}, {"desc": "806", "fix": "807"}, {"desc": "808", "fix": "809"}, {"desc": "801", "fix": "810"}, {"desc": "811", "fix": "812"}, {"desc": "813", "fix": "814"}, {"desc": "801", "fix": "815"}, {"desc": "816", "fix": "817"}, {"desc": "818", "fix": "819"}, {"desc": "820", "fix": "821"}, {"desc": "822", "fix": "823"}, {"desc": "820", "fix": "824"}, {"desc": "825", "fix": "826"}, {"desc": "827", "fix": "828"}, {"desc": "801", "fix": "829"}, {"desc": "830", "fix": "831"}, {"desc": "801", "fix": "832"}, {"desc": "830", "fix": "833"}, {"desc": "834", "fix": "835"}, {"desc": "801", "fix": "836"}, {"desc": "837", "fix": "838"}, {"desc": "839", "fix": "840"}, {"desc": "841", "fix": "842"}, {"desc": "843", "fix": "844"}, {"desc": "845", "fix": "846"}, {"desc": "847", "fix": "848"}, {"desc": "849", "fix": "850"}, "Update the dependencies array to be: [currentDate]", {"range": "851", "text": "852"}, "Update the dependencies array to be: [debouncedSearchTerm, filters, selectedCategoryId, selectedStatus, updateFilters, user.grade_level]", {"range": "853", "text": "854"}, "Update the dependencies array to be: [currentPage, debouncedSearchTerm, filterStatus, permissions.isSuperAdmin, user.grade_level]", {"range": "855", "text": "856"}, "Update the dependencies array to be: [duration, handleClose]", {"range": "857", "text": "858"}, "Update the dependencies array to be: [imagePath, imageUrl]", {"range": "859", "text": "860"}, {"range": "861", "text": "852"}, "Update the dependencies array to be: [handleDeepLinkHighlighting, location]", {"range": "862", "text": "863"}, "Update the dependencies array to be: [clearCacheIfUserChanged, useAdminService, getCurrentUserContext, filters, service]", {"range": "864", "text": "865"}, "Update the dependencies array to be: [useAdminService, clearCacheIfUserChanged, fetchAnnouncements]", {"range": "866", "text": "867"}, {"range": "868", "text": "860"}, "Update the dependencies array to be: [goToNext, goToPrevious, isOpen, onClose]", {"range": "869", "text": "870"}, "Update the dependencies array to be: [announcement.announcement_id, isOpen, clearImageError, clearPendingDeletes, resetModalForNewAnnouncement, announcement, refreshImages]", {"range": "871", "text": "872"}, {"range": "873", "text": "860"}, "Update the dependencies array to be: [getCurrentUserContext, announcementId]", {"range": "874", "text": "875"}, "Update the dependencies array to be: [calendarId, onError]", {"range": "876", "text": "877"}, "Update the dependencies array to be: [calendarId, onError, onSuccess, refreshImages]", {"range": "878", "text": "879"}, "Update the dependencies array to be: [onError, onSuccess, refreshImages]", {"range": "880", "text": "881"}, {"range": "882", "text": "879"}, "Update the dependencies array to be: [pendingDeletes, refreshImages]", {"range": "883", "text": "884"}, "Update the dependencies array to be: [calendarId, refreshImages]", {"range": "885", "text": "886"}, {"range": "887", "text": "860"}, "Update the dependencies array to be: [images]", {"range": "888", "text": "889"}, {"range": "890", "text": "860"}, {"range": "891", "text": "889"}, "Update the dependencies array to be: [currentYear, loadData]", {"range": "892", "text": "893"}, {"range": "894", "text": "860"}, "Update the dependencies array to be: [currentPage, loadEvents, searchTerm]", {"range": "895", "text": "896"}, "Update the dependencies array to be: [currentPage, loadStudents, searchTerm]", {"range": "897", "text": "898"}, "Update the dependencies array to be: [currentPage, loadAnnouncements, searchTerm]", {"range": "899", "text": "900"}, "Update the dependencies array to be: [isPlaying, currentSlide, createSlideContent]", {"range": "901", "text": "902"}, "Update the dependencies array to be: [handleControlCommand]", {"range": "903", "text": "904"}, "Update the dependencies array to be: [permissions.canManageAdmins, currentPage, itemsPerPage, searchTerm, positionFilter, statusFilter, loadAdmins]", {"range": "905", "text": "906"}, "Update the dependencies array to be: [category, subcategory, mode, isOpen, isEditMode, isSubcategoryMode]", {"range": "907", "text": "908"}, [8001, 8052], "[currentDate]", [4777, 4853], "[debouncedSearchTerm, filters, selectedCategoryId, selectedStatus, updateFilters, user.grade_level]", [4681, 4735], "[currentPage, debouncedSearchTerm, filterStatus, permissions.isSuperAdmin, user.grade_level]", [900, 910], "[duration, handleClose]", [1735, 1746], "[imagePath, imageUrl]", [3099, 3126], [2143, 2153], "[handleDeepLinkHighlighting, location]", [4408, 4507], "[clearCacheIfUserChanged, useAdminService, getCurrentUserContext, filters, service]", [11180, 11222], "[useAdminService, clearCacheIfUserChanged, fetchAnnouncements]", [1595, 1606], [4614, 4631], "[goTo<PERSON><PERSON>t, goToPrevious, isOpen, onClose]", [6335, 6442], "[announcement.announcement_id, isOpen, clearImageError, clearPendingDeletes, resetModalForNewAnnouncement, announcement, refreshImages]", [1909, 1920], [4906, 4957], "[getCurrentUserContext, announcementId]", [4049, 4061], "[calendarId, onError]", [5454, 5481], "[calendarId, onError, onSuccess, refreshImages]", [6495, 6510], "[onError, onSuccess, refreshImages]", [7671, 7698], [9871, 9887], "[pendingDeletes, refreshImages]", [10076, 10088], "[calendarId, refreshImages]", [2737, 2748], [10514, 10516], "[images]", [1825, 1836], [11634, 11636], [1557, 1570], "[currentYear, loadData]", [3031, 3042], [958, 983], "[currentPage, loadEvents, searchTerm]", [938, 963], "[currentPage, loadStudents, searchTerm]", [1032, 1057], "[currentPage, loadAnnouncements, searchTerm]", [5902, 5927], "[isPlaying, currentSlide, createSlideContent]", [6428, 6430], "[handleControlCommand]", [2172, 2270], "[permissions.canManageAdmins, currentPage, itemsPerPage, searchTerm, positionFilter, statusFilter, loadAdmins]", [2225, 2262], "[category, subcategory, mode, isOpen, isEditMode, isSubcategoryMode]"]